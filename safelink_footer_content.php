<?php defined('ABSPATH') || exit; ?>
<?php
// Retrieve User Link from Cookies
$userLinkValue = isset($_COOKIE['userLink']) ? htmlspecialchars($_COOKIE['userLink']) : null;

// Function to detect Android device
function isAndroidDevice() {
    return strpos($_SERVER['HTTP_USER_AGENT'], 'Android') !== false;
}

// Function to redirect safely
function redirectToSafeLink($alias) {
    $reddomain = ["https://teralinktree.com"];
    $selected_domain = $reddomain[array_rand($reddomain)];
    $sanitizedAlias = htmlspecialchars($alias, ENT_QUOTES, 'UTF-8');
    $url = "https://$selected_domain/$sanitizedAlias";

    if (isAndroidDevice()) {
        $url = "intent://$selected_domain/$sanitizedAlias#Intent;scheme=https;package=com.android.chrome;end;";
    }
    return $url;
}

$realLink = redirectToSafeLink($_COOKIE['userLink']);
?>

<script>
let pageNo = sessionStorage.getItem('pageNo') || 1;
sessionStorage.setItem('pageNo', pageNo);
console.log("Current page number: " + pageNo);
</script>

<?php if ($userLinkValue): ?>
<center>  
    <?php renderAdHook('adsence_footer_up'); ?>
</center>

<center>
    <button id="new-page-button" class="hidden" onclick="openNewPage()">Click to Continue</button>

    <a id="real-link" href="<?php echo $realLink; ?>" 
    style="text-decoration: none; display: none;">
        <button class="Main-link">Open Main Link</button>
    </a>

    <div class="footer-ad"></div>

    <div id="footer" class="footer-section">
        <p>This is the footer content.</p>
    </div>
</center>

<script>
let timeLeft = 20;
const timerElement = document.getElementById('timer');
const robotButton = document.getElementById('robot-button');
const adclickButton = document.getElementById('adclick-button');
const continueButton = document.getElementById('new-page-button');
const realLink = document.getElementById('real-link');
const infoButton = document.getElementById('info-button');

function updateTimer() {
    if (timeLeft > 0) {
        timerElement.textContent = timeLeft;
        timeLeft--;
        setTimeout(updateTimer, 1000);
    } else {
        document.querySelector('.timer-section').style.display = 'none';
        continueButton.classList.remove('hidden');
        let pageNo = parseInt(sessionStorage.getItem('pageNo')) || 1;
        if (pageNo >= 2) {
            robotButton.classList.remove('hidden');
            infoButton.classList.add('hidden');
        } else {
            adclickButton.classList.remove('hidden');
        }
    }
}

function scrollToFooter() {
    const footer = document.getElementById('footer');
    if (footer) {
        footer.scrollIntoView({ behavior: 'smooth' });
    } else {
        console.error("Footer element with ID 'footer' not found.");
    }
}

function openNewPage() {
    let pageNo = parseInt(sessionStorage.getItem('pageNo')) || 1;

    if (pageNo >= 2) {
        sessionStorage.setItem('pageNo', 1);
        realLink.style.display = 'inline';
        continueButton.style.display = 'none';
    } else {
        sessionStorage.setItem('pageNo', pageNo + 1);
        const randomPostLink = "<?= get_random_post_link(); ?>";
        window.location.href = randomPostLink;
    }
}

updateTimer();
</script>
<?php endif; ?>

<?php
/**
 * Plugin Name: Safelink Ad Injector
 * Description: Injects custom PHP safelink header and footer files with dynamic AdSense code from the admin dashboard.
 * Version: 1.1
 * Author: Your Name
 */

// Admin Settings Page
add_action('admin_menu', 'safelink_ad_injector_add_menu');
function safelink_ad_injector_add_menu() {
    add_menu_page('Safelink Ad Settings', 'Safelink Ads', 'manage_options', 'safelink-ad-settings', 'safelink_ad_settings_page');
}

add_action('admin_init', 'safelink_ad_settings_init');
function safelink_ad_settings_init() {
    register_setting('safelink_ad_group', 'safelink_footer_ad_code');
    register_setting('safelink_ad_group', 'safelink_header_ad_code');
}

function safelink_ad_settings_page() {
    ?>
    <div class="wrap">
        <h1>Safelink Ad Code Settings</h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('safelink_ad_group');
            do_settings_sections('safelink_ad_group');
            ?>
            <h2>Header Ad Code</h2>
            <textarea name="safelink_header_ad_code" rows="8" style="width:100%;"><?php echo esc_textarea(get_option('safelink_header_ad_code', '')); ?></textarea>

            <h2>Footer Ad Code</h2>
            <textarea name="safelink_footer_ad_code" rows="8" style="width:100%;"><?php echo esc_textarea(get_option('safelink_footer_ad_code', '')); ?></textarea>

            <p class="submit">
                <input type="submit" class="button-primary" value="Save Ad Code">
            </p>
        </form>
    </div>
    <?php
}

// Include header and footer safelink files dynamically
add_action('wp_head', 'safelink_include_header_template', 1);
add_action('wp_footer', 'safelink_include_footer_template', 1);

function safelink_include_header_template() {
    include plugin_dir_path(__FILE__) . 'safelink_header.php';
}

function safelink_include_footer_template() {
    include plugin_dir_path(__FILE__) . 'safelink_footer_content.php';
}

// Hooks to render ad code from dashboard
function customAdHook($hookName) {
    if ($hookName === 'adsence_footer_up') {
        $code = get_option('safelink_footer_ad_code', '');
        if (!empty($code)) {
            echo "\n<!-- Dynamic Footer Ad Code -->\n" . $code . "\n<!-- End Footer Ad Code -->\n";
        }
    } elseif ($hookName === 'adsence_header_up') {
        $code = get_option('safelink_header_ad_code', '');
        if (!empty($code)) {
            echo "\n<!-- Dynamic Header Ad Code -->\n" . $code . "\n<!-- End Header Ad Code -->\n";
        }
    }
}

function renderAdHook($hookName) {
    if (function_exists('customAdHook')) {
        customAdHook($hookName);
    }
}
?>
